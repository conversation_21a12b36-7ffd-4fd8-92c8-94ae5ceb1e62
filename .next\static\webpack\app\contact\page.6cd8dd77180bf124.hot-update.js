"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/contact/page",{

/***/ "(app-pages-browser)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAuth() {\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Fetch current session and user data\n    const fetchSession = async ()=>{\n        try {\n            console.log(\"AuthContext: Fetching session\");\n            const response = await fetch(\"/api/auth/session\", {\n                method: \"GET\",\n                credentials: \"include\"\n            });\n            if (!response.ok) {\n                console.log(\"AuthContext: No active session\");\n                setUser(null);\n                setSession(null);\n                return;\n            }\n            const data = await response.json();\n            console.log(\"AuthContext: Session fetched successfully\");\n            setUser(data.user);\n            setSession(data.session);\n        } catch (error) {\n            console.error(\"AuthContext: Error fetching session:\", error);\n            setUser(null);\n            setSession(null);\n        }\n    };\n    // Initialize auth state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAuth = async ()=>{\n            console.log(\"AuthContext: Initializing auth\");\n            await fetchSession();\n            setLoading(false);\n        };\n        initializeAuth();\n    }, []);\n    // Sign up function\n    const signUp = async (email, password, userData)=>{\n        try {\n            console.log(\"AuthContext: Attempting signup\");\n            const response = await fetch(\"/api/auth/register\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify({\n                    email,\n                    password,\n                    fullName: userData.full_name,\n                    phone: userData.phone\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                console.error(\"AuthContext: Signup error:\", data.error);\n                return {\n                    data: null,\n                    error: data.error\n                };\n            }\n            console.log(\"AuthContext: Signup successful\");\n            return {\n                data: data.user,\n                error: null\n            };\n        } catch (error) {\n            console.error(\"AuthContext: Signup network error:\", error);\n            return {\n                data: null,\n                error: \"Network error. Please try again.\"\n            };\n        }\n    };\n    // Sign in function\n    const signIn = async (email, password)=>{\n        try {\n            console.log(\"AuthContext: Attempting signin\");\n            const response = await fetch(\"/api/auth/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                console.error(\"AuthContext: Signin error:\", data.error);\n                return {\n                    data: null,\n                    error: data.error\n                };\n            }\n            console.log(\"AuthContext: Signin successful\");\n            // Update local state\n            setUser(data.user);\n            setSession(data.session);\n            return {\n                data: data.user,\n                error: null\n            };\n        } catch (error) {\n            console.error(\"AuthContext: Signin network error:\", error);\n            return {\n                data: null,\n                error: \"Network error. Please try again.\"\n            };\n        }\n    };\n    // Sign out function\n    const signOut = async ()=>{\n        try {\n            console.log(\"AuthContext: Attempting signout\");\n            const response = await fetch(\"/api/auth/logout\", {\n                method: \"POST\",\n                credentials: \"include\"\n            });\n            // Clear local state regardless of API response\n            setUser(null);\n            setSession(null);\n            if (!response.ok) {\n                console.warn(\"AuthContext: Signout API error, but local state cleared\");\n            } else {\n                console.log(\"AuthContext: Signout successful\");\n            }\n            return {\n                error: null\n            };\n        } catch (error) {\n            console.error(\"AuthContext: Signout network error:\", error);\n            // Still clear local state\n            setUser(null);\n            setSession(null);\n            return {\n                error: null\n            }; // Don't fail signout for network errors\n        }\n    };\n    // Reset password function\n    const resetPassword = async (email)=>{\n        try {\n            console.log(\"AuthContext: Reset password not implemented via API yet\");\n            return {\n                error: \"Password reset functionality will be available soon\"\n            };\n        } catch (error) {\n            return {\n                error: error.message\n            };\n        }\n    };\n    // Update profile function\n    const updateProfile = async (updates)=>{\n        if (!user) {\n            return {\n                data: null,\n                error: \"No user logged in\"\n            };\n        }\n        try {\n            console.log(\"AuthContext: Update profile not implemented via API yet\");\n            return {\n                data: null,\n                error: \"Profile update functionality will be available soon\"\n            };\n        } catch (error) {\n            return {\n                data: null,\n                error: error.message\n            };\n        }\n    };\n    // Refresh user data\n    const refreshUser = async ()=>{\n        console.log(\"AuthContext: Refreshing user data\");\n        await fetchSession();\n    };\n    const value = {\n        user,\n        session,\n        loading,\n        signUp,\n        signIn,\n        signOut,\n        resetPassword,\n        updateProfile,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 217,\n        columnNumber: 5\n    }, this);\n}\n_s1(AuthProvider, \"sIDOCMze9iVqwxkgWIhOu8vskSI=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/AuthContext.tsx\n"));

/***/ })

});