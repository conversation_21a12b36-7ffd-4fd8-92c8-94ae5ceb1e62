{"c": ["app/page", "app/layout", "app/about/page", "app/trips/page", "app/contact/page", "app/rural-initiative/page", "app/trips/[slug]/page", "app/auth/login/page", "app/auth/register/page", "webpack"], "r": ["_app-pages-browser_node_modules_ws_browser_js"], "m": ["(app-pages-browser)/./lib/supabase-client.ts", "(app-pages-browser)/./node_modules/@supabase/auth-js/dist/module/AuthAdminApi.js", "(app-pages-browser)/./node_modules/@supabase/auth-js/dist/module/AuthClient.js", "(app-pages-browser)/./node_modules/@supabase/auth-js/dist/module/GoTrueAdminApi.js", "(app-pages-browser)/./node_modules/@supabase/auth-js/dist/module/GoTrueClient.js", "(app-pages-browser)/./node_modules/@supabase/auth-js/dist/module/index.js", "(app-pages-browser)/./node_modules/@supabase/auth-js/dist/module/lib/base64url.js", "(app-pages-browser)/./node_modules/@supabase/auth-js/dist/module/lib/constants.js", "(app-pages-browser)/./node_modules/@supabase/auth-js/dist/module/lib/errors.js", "(app-pages-browser)/./node_modules/@supabase/auth-js/dist/module/lib/fetch.js", "(app-pages-browser)/./node_modules/@supabase/auth-js/dist/module/lib/helpers.js", "(app-pages-browser)/./node_modules/@supabase/auth-js/dist/module/lib/local-storage.js", "(app-pages-browser)/./node_modules/@supabase/auth-js/dist/module/lib/locks.js", "(app-pages-browser)/./node_modules/@supabase/auth-js/dist/module/lib/polyfills.js", "(app-pages-browser)/./node_modules/@supabase/auth-js/dist/module/lib/types.js", "(app-pages-browser)/./node_modules/@supabase/auth-js/dist/module/lib/version.js", "(app-pages-browser)/./node_modules/@supabase/functions-js/dist/module/FunctionsClient.js", "(app-pages-browser)/./node_modules/@supabase/functions-js/dist/module/helper.js", "(app-pages-browser)/./node_modules/@supabase/functions-js/dist/module/types.js", "(app-pages-browser)/./node_modules/@supabase/node-fetch/browser.js", "(app-pages-browser)/./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestBuilder.js", "(app-pages-browser)/./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestClient.js", "(app-pages-browser)/./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestError.js", "(app-pages-browser)/./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestFilterBuilder.js", "(app-pages-browser)/./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestQueryBuilder.js", "(app-pages-browser)/./node_modules/@supabase/postgrest-js/dist/cjs/PostgrestTransformBuilder.js", "(app-pages-browser)/./node_modules/@supabase/postgrest-js/dist/cjs/constants.js", "(app-pages-browser)/./node_modules/@supabase/postgrest-js/dist/cjs/index.js", "(app-pages-browser)/./node_modules/@supabase/postgrest-js/dist/cjs/version.js", "(app-pages-browser)/./node_modules/@supabase/postgrest-js/dist/esm/wrapper.mjs", "(app-pages-browser)/./node_modules/@supabase/realtime-js/dist/module/RealtimeChannel.js", "(app-pages-browser)/./node_modules/@supabase/realtime-js/dist/module/RealtimeClient.js", "(app-pages-browser)/./node_modules/@supabase/realtime-js/dist/module/RealtimePresence.js", "(app-pages-browser)/./node_modules/@supabase/realtime-js/dist/module/index.js", "(app-pages-browser)/./node_modules/@supabase/realtime-js/dist/module/lib/constants.js", "(app-pages-browser)/./node_modules/@supabase/realtime-js/dist/module/lib/push.js", "(app-pages-browser)/./node_modules/@supabase/realtime-js/dist/module/lib/serializer.js", "(app-pages-browser)/./node_modules/@supabase/realtime-js/dist/module/lib/timer.js", "(app-pages-browser)/./node_modules/@supabase/realtime-js/dist/module/lib/transformers.js", "(app-pages-browser)/./node_modules/@supabase/realtime-js/dist/module/lib/version.js", "(app-pages-browser)/./node_modules/@supabase/ssr/dist/module/cookies.js", "(app-pages-browser)/./node_modules/@supabase/ssr/dist/module/createBrowserClient.js", "(app-pages-browser)/./node_modules/@supabase/ssr/dist/module/createServerClient.js", "(app-pages-browser)/./node_modules/@supabase/ssr/dist/module/index.js", "(app-pages-browser)/./node_modules/@supabase/ssr/dist/module/types.js", "(app-pages-browser)/./node_modules/@supabase/ssr/dist/module/utils/base64url.js", "(app-pages-browser)/./node_modules/@supabase/ssr/dist/module/utils/chunker.js", "(app-pages-browser)/./node_modules/@supabase/ssr/dist/module/utils/constants.js", "(app-pages-browser)/./node_modules/@supabase/ssr/dist/module/utils/helpers.js", "(app-pages-browser)/./node_modules/@supabase/ssr/dist/module/utils/index.js", "(app-pages-browser)/./node_modules/@supabase/ssr/dist/module/version.js", "(app-pages-browser)/./node_modules/@supabase/storage-js/dist/module/StorageClient.js", "(app-pages-browser)/./node_modules/@supabase/storage-js/dist/module/lib/constants.js", "(app-pages-browser)/./node_modules/@supabase/storage-js/dist/module/lib/errors.js", "(app-pages-browser)/./node_modules/@supabase/storage-js/dist/module/lib/fetch.js", "(app-pages-browser)/./node_modules/@supabase/storage-js/dist/module/lib/helpers.js", "(app-pages-browser)/./node_modules/@supabase/storage-js/dist/module/lib/version.js", "(app-pages-browser)/./node_modules/@supabase/storage-js/dist/module/packages/StorageBucketApi.js", "(app-pages-browser)/./node_modules/@supabase/storage-js/dist/module/packages/StorageFileApi.js", "(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/SupabaseClient.js", "(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/index.js", "(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/lib/SupabaseAuthClient.js", "(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/lib/constants.js", "(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/lib/fetch.js", "(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/lib/helpers.js", "(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/lib/version.js", "(app-pages-browser)/./node_modules/cookie/dist/index.js", "(app-pages-browser)/./node_modules/ws/browser.js"]}