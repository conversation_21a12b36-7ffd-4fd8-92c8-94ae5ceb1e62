"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAuth() {\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Fetch current session and user data\n    const fetchSession = async ()=>{\n        try {\n            console.log(\"AuthContext: Fetching session\");\n            const response = await fetch(\"/api/auth/session\", {\n                method: \"GET\",\n                credentials: \"include\"\n            });\n            if (!response.ok) {\n                console.log(\"AuthContext: No active session\");\n                setUser(null);\n                setSession(null);\n                return;\n            }\n            const data = await response.json();\n            console.log(\"AuthContext: Session fetched successfully\");\n            setUser(data.user);\n            setSession(data.session);\n        } catch (error) {\n            console.error(\"AuthContext: Error fetching session:\", error);\n            setUser(null);\n            setSession(null);\n        }\n    };\n    // Initialize auth state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAuth = async ()=>{\n            console.log(\"AuthContext: Initializing auth\");\n            await fetchSession();\n            setLoading(false);\n        };\n        initializeAuth();\n    }, []);\n    // Sign up function\n    const signUp = async (email, password, userData)=>{\n        try {\n            console.log(\"AuthContext: Attempting signup\");\n            const response = await fetch(\"/api/auth/register\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify({\n                    email,\n                    password,\n                    fullName: userData.full_name,\n                    phone: userData.phone\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                console.error(\"AuthContext: Signup error:\", data.error);\n                return {\n                    data: null,\n                    error: data.error\n                };\n            }\n            console.log(\"AuthContext: Signup successful\");\n            return {\n                data: data.user,\n                error: null\n            };\n        } catch (error) {\n            console.error(\"AuthContext: Signup network error:\", error);\n            return {\n                data: null,\n                error: \"Network error. Please try again.\"\n            };\n        }\n    };\n    // Sign in function\n    const signIn = async (email, password)=>{\n        try {\n            console.log(\"AuthContext: Attempting signin\");\n            const response = await fetch(\"/api/auth/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                console.error(\"AuthContext: Signin error:\", data.error);\n                return {\n                    data: null,\n                    error: data.error\n                };\n            }\n            console.log(\"AuthContext: Signin successful\");\n            // Update local state\n            setUser(data.user);\n            setSession(data.session);\n            return {\n                data: data.user,\n                error: null\n            };\n        } catch (error) {\n            console.error(\"AuthContext: Signin network error:\", error);\n            return {\n                data: null,\n                error: \"Network error. Please try again.\"\n            };\n        }\n    };\n    // Sign out function\n    const signOut = async ()=>{\n        try {\n            console.log(\"AuthContext: Attempting signout\");\n            const response = await fetch(\"/api/auth/logout\", {\n                method: \"POST\",\n                credentials: \"include\"\n            });\n            // Clear local state regardless of API response\n            setUser(null);\n            setSession(null);\n            if (!response.ok) {\n                console.warn(\"AuthContext: Signout API error, but local state cleared\");\n            } else {\n                console.log(\"AuthContext: Signout successful\");\n            }\n            return {\n                error: null\n            };\n        } catch (error) {\n            console.error(\"AuthContext: Signout network error:\", error);\n            // Still clear local state\n            setUser(null);\n            setSession(null);\n            return {\n                error: null\n            }; // Don't fail signout for network errors\n        }\n    };\n    // Reset password function\n    const resetPassword = async (email)=>{\n        try {\n            console.log(\"AuthContext: Reset password not implemented via API yet\");\n            return {\n                error: \"Password reset functionality will be available soon\"\n            };\n        } catch (error) {\n            return {\n                error: error.message\n            };\n        }\n    };\n    // Update profile function\n    const updateProfile = async (updates)=>{\n        if (!user) {\n            return {\n                data: null,\n                error: \"No user logged in\"\n            };\n        }\n        try {\n            console.log(\"AuthContext: Update profile not implemented via API yet\");\n            return {\n                data: null,\n                error: \"Profile update functionality will be available soon\"\n            };\n        } catch (error) {\n            return {\n                data: null,\n                error: error.message\n            };\n        }\n    };\n    // Refresh user data\n    const refreshUser = async ()=>{\n        console.log(\"AuthContext: Refreshing user data\");\n        await fetchSession();\n    };\n    const value = {\n        user,\n        session,\n        loading,\n        signUp,\n        signIn,\n        signOut,\n        resetPassword,\n        updateProfile,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 218,\n        columnNumber: 5\n    }, this);\n}\n_s1(AuthProvider, \"sIDOCMze9iVqwxkgWIhOu8vskSI=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/AuthContext.tsx\n"));

/***/ })

});