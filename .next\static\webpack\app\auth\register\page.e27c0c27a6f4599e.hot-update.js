"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/register/page",{

/***/ "(app-pages-browser)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAuth() {\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Fetch current session and user data\n    const fetchSession = async ()=>{\n        try {\n            console.log(\"AuthContext: Fetching session\");\n            const response = await fetch(\"/api/auth/session\", {\n                method: \"GET\",\n                credentials: \"include\"\n            });\n            if (!response.ok) {\n                console.log(\"AuthContext: No active session\");\n                setUser(null);\n                setSession(null);\n                return;\n            }\n            const data = await response.json();\n            console.log(\"AuthContext: Session fetched successfully\");\n            setUser(data.user);\n            setSession(data.session);\n        } catch (error) {\n            console.error(\"AuthContext: Error fetching session:\", error);\n            setUser(null);\n            setSession(null);\n        }\n    };\n    // Initialize auth state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAuth = async ()=>{\n            try {\n                const { data: { session }, error } = await supabase.auth.getSession();\n                if (error) {\n                    console.error(\"Error getting session:\", error);\n                    setLoading(false);\n                    return;\n                }\n                setSession(session);\n                if (session === null || session === void 0 ? void 0 : session.user) {\n                    const userProfile = await fetchUserProfile(session.user.id);\n                    setUser(userProfile);\n                }\n            } catch (error) {\n                console.error(\"Error initializing auth:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        initializeAuth();\n        // Listen for auth changes\n        const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session)=>{\n            var _session_user;\n            console.log(\"Auth state changed:\", event, session === null || session === void 0 ? void 0 : (_session_user = session.user) === null || _session_user === void 0 ? void 0 : _session_user.id);\n            setSession(session);\n            if (session === null || session === void 0 ? void 0 : session.user) {\n                const userProfile = await fetchUserProfile(session.user.id);\n                setUser(userProfile);\n            } else {\n                setUser(null);\n            }\n            setLoading(false);\n        });\n        return ()=>{\n            subscription.unsubscribe();\n        };\n    }, []);\n    // Sign up function\n    const signUp = async (email, password, userData)=>{\n        try {\n            const { data, error } = await supabase.auth.signUp({\n                email,\n                password,\n                options: {\n                    data: {\n                        full_name: userData.full_name,\n                        phone: userData.phone\n                    }\n                }\n            });\n            if (error) {\n                return {\n                    data: null,\n                    error: error.message\n                };\n            }\n            // Create user profile\n            if (data.user) {\n                const { error: profileError } = await supabase.from(\"users\").insert({\n                    id: data.user.id,\n                    email: data.user.email,\n                    full_name: userData.full_name,\n                    phone: userData.phone,\n                    role: \"customer\"\n                });\n                if (profileError) {\n                    console.error(\"Error creating user profile:\", profileError);\n                    return {\n                        data: null,\n                        error: \"Failed to create user profile\"\n                    };\n                }\n            }\n            return {\n                data,\n                error: null\n            };\n        } catch (error) {\n            return {\n                data: null,\n                error: error.message\n            };\n        }\n    };\n    // Sign in function\n    const signIn = async (email, password)=>{\n        try {\n            const { data, error } = await supabase.auth.signInWithPassword({\n                email,\n                password\n            });\n            if (error) {\n                return {\n                    data: null,\n                    error: error.message\n                };\n            }\n            return {\n                data,\n                error: null\n            };\n        } catch (error) {\n            return {\n                data: null,\n                error: error.message\n            };\n        }\n    };\n    // Sign out function\n    const signOut = async ()=>{\n        try {\n            const { error } = await supabase.auth.signOut();\n            if (error) {\n                return {\n                    error: error.message\n                };\n            }\n            return {\n                error: null\n            };\n        } catch (error) {\n            return {\n                error: error.message\n            };\n        }\n    };\n    // Reset password function\n    const resetPassword = async (email)=>{\n        try {\n            const { error } = await supabase.auth.resetPasswordForEmail(email, {\n                redirectTo: \"\".concat(window.location.origin, \"/auth/reset-password\")\n            });\n            if (error) {\n                return {\n                    error: error.message\n                };\n            }\n            return {\n                error: null\n            };\n        } catch (error) {\n            return {\n                error: error.message\n            };\n        }\n    };\n    // Update profile function\n    const updateProfile = async (updates)=>{\n        if (!user) {\n            return {\n                data: null,\n                error: \"No user logged in\"\n            };\n        }\n        try {\n            const { data, error } = await supabase.from(\"users\").update({\n                ...updates,\n                updated_at: new Date().toISOString()\n            }).eq(\"id\", user.id).select().single();\n            if (error) {\n                return {\n                    data: null,\n                    error: error.message\n                };\n            }\n            setUser(data);\n            return {\n                data,\n                error: null\n            };\n        } catch (error) {\n            return {\n                data: null,\n                error: error.message\n            };\n        }\n    };\n    // Refresh user data\n    const refreshUser = async ()=>{\n        if (!(session === null || session === void 0 ? void 0 : session.user)) return;\n        const userProfile = await fetchUserProfile(session.user.id);\n        setUser(userProfile);\n    };\n    const value = {\n        user,\n        session,\n        loading,\n        signUp,\n        signIn,\n        signOut,\n        resetPassword,\n        updateProfile,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 256,\n        columnNumber: 5\n    }, this);\n}\n_s1(AuthProvider, \"sIDOCMze9iVqwxkgWIhOu8vskSI=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/AuthContext.tsx\n"));

/***/ })

});