"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/auth/register/route";
exports.ids = ["app/api/auth/register/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fregister%2Froute&page=%2Fapi%2Fauth%2Fregister%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister%2Froute.ts&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fregister%2Froute&page=%2Fapi%2Fauth%2Fregister%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister%2Froute.ts&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   headerHooks: () => (/* binding */ headerHooks),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage),\n/* harmony export */   staticGenerationBailout: () => (/* binding */ staticGenerationBailout)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_peebs_Documents_projects_p7_comprehensive_app_api_auth_register_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/auth/register/route.ts */ \"(rsc)/./app/api/auth/register/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/auth/register/route\",\n        pathname: \"/api/auth/register\",\n        filename: \"route\",\n        bundlePath: \"app/api/auth/register/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\app\\\\api\\\\auth\\\\register\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_peebs_Documents_projects_p7_comprehensive_app_api_auth_register_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks, headerHooks, staticGenerationBailout } = routeModule;\nconst originalPathname = \"/api/auth/register/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fregister%2Froute&page=%2Fapi%2Fauth%2Fregister%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister%2Froute.ts&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/auth/register/route.ts":
/*!****************************************!*\
  !*** ./app/api/auth/register/route.ts ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/web/exports/next-response */ \"(rsc)/./node_modules/next/dist/server/web/exports/next-response.js\");\n/* harmony import */ var _lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase-server */ \"(rsc)/./lib/supabase-server.ts\");\n\n\nasync function POST(request) {\n    try {\n        console.log(\"Register API: Starting registration process\");\n        const body = await request.json();\n        console.log(\"Register API: Received body:\", {\n            email: body.email,\n            hasPassword: !!body.password,\n            fullName: body.fullName,\n            phone: body.phone\n        });\n        const { email, password, fullName, phone } = body;\n        // Validate required fields\n        if (!email || !password || !fullName || !phone) {\n            console.log(\"Register API: Missing required fields\");\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Missing required fields\"\n            }, {\n                status: 400\n            });\n        }\n        // Validate email format\n        const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n        if (!emailRegex.test(email)) {\n            console.log(\"Register API: Invalid email format\");\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Invalid email format\"\n            }, {\n                status: 400\n            });\n        }\n        // Validate password length\n        if (password.length < 8) {\n            console.log(\"Register API: Password too short\");\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Password must be at least 8 characters long\"\n            }, {\n                status: 400\n            });\n        }\n        const supabase = (0,_lib_supabase_server__WEBPACK_IMPORTED_MODULE_1__.createServerSupabase)();\n        console.log(\"Register API: Created Supabase client\");\n        // Check if user already exists\n        const { data: existingUser } = await supabase.from(\"users\").select(\"id\").eq(\"email\", email).single();\n        if (existingUser) {\n            console.log(\"Register API: User already exists\");\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"User with this email already exists\"\n            }, {\n                status: 409\n            });\n        }\n        console.log(\"Register API: Attempting to create auth user\");\n        // Create auth user\n        const { data: authData, error: authError } = await supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                data: {\n                    full_name: fullName,\n                    phone: phone\n                }\n            }\n        });\n        if (authError) {\n            console.error(\"Register API: Auth error:\", authError);\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: authError.message\n            }, {\n                status: 400\n            });\n        }\n        if (!authData.user) {\n            console.error(\"Register API: No user returned from auth\");\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Failed to create user account\"\n            }, {\n                status: 500\n            });\n        }\n        console.log(\"Register API: Auth user created successfully:\", authData.user.id);\n        // Create user profile\n        const { error: profileError } = await supabase.from(\"users\").insert({\n            id: authData.user.id,\n            email: authData.user.email,\n            full_name: fullName,\n            phone: phone,\n            role: \"customer\",\n            email_verified: authData.user.email_confirmed_at ? true : false\n        });\n        if (profileError) {\n            console.error(\"Register API: Profile creation error:\", profileError);\n            // Try to clean up the auth user if profile creation fails\n            try {\n                await supabase.auth.admin.deleteUser(authData.user.id);\n            } catch (cleanupError) {\n                console.error(\"Register API: Failed to cleanup auth user:\", cleanupError);\n            }\n            return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n                error: \"Failed to create user profile\"\n            }, {\n                status: 500\n            });\n        }\n        console.log(\"Register API: User profile created successfully\");\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            message: \"User registered successfully\",\n            user: {\n                id: authData.user.id,\n                email: authData.user.email,\n                full_name: fullName,\n                phone: phone,\n                email_confirmed: authData.user.email_confirmed_at ? true : false\n            }\n        });\n    } catch (error) {\n        console.error(\"Register API: Unexpected error:\", error);\n        return next_dist_server_web_exports_next_response__WEBPACK_IMPORTED_MODULE_0__[\"default\"].json({\n            error: \"Internal server error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/auth/register/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/supabase-server.ts":
/*!********************************!*\
  !*** ./lib/supabase-server.ts ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createServerSupabase: () => (/* binding */ createServerSupabase),\n/* harmony export */   verifySession: () => (/* binding */ verifySession)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/module/index.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/headers */ \"(rsc)/./node_modules/next/headers.js\");\n/* harmony import */ var next_headers__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_headers__WEBPACK_IMPORTED_MODULE_1__);\n\n\n// Environment variables with fallbacks for development\nconst supabaseUrl = \"https://soaoagcuubtzojytoati.supabase.co\" || 0;\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNvYW9hZ2N1dWJ0em9qeXRvYXRpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg0ODQyOTksImV4cCI6MjA2NDA2MDI5OX0.h0NpruyXbMY9bN-RB_Ng_s_uscP6G3VW_R0rM91DtW0\" || 0;\n// Server-side client for API routes, Server Components, and Server Actions\nconst createServerSupabase = ()=>{\n    const cookieStore = (0,next_headers__WEBPACK_IMPORTED_MODULE_1__.cookies)();\n    return (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(supabaseUrl, supabaseAnonKey, {\n        cookies: {\n            getAll () {\n                return cookieStore.getAll();\n            },\n            setAll (cookiesToSet) {\n                try {\n                    cookiesToSet.forEach(({ name, value, options })=>cookieStore.set(name, value, options));\n                } catch  {\n                // The `setAll` method was called from a Server Component.\n                // This can be ignored if you have middleware refreshing\n                // user sessions.\n                }\n            }\n        }\n    });\n};\n// Server-side session verification function\nconst verifySession = async (requiredRole)=>{\n    try {\n        const supabase = createServerSupabase();\n        const { data: { session }, error } = await supabase.auth.getSession();\n        if (error) {\n            console.error(\"Error getting session:\", error);\n            return null;\n        }\n        if (!session) {\n            return null;\n        }\n        // If a specific role is required, check user role\n        if (requiredRole) {\n            const { data: user, error: userError } = await supabase.from(\"users\").select(\"role\").eq(\"id\", session.user.id).single();\n            if (userError) {\n                console.error(\"Error getting user role:\", userError);\n                return null;\n            }\n            if (!user || user.role !== requiredRole) {\n                return null;\n            }\n        }\n        return session;\n    } catch (error) {\n        console.error(\"Error verifying session:\", error);\n        return null;\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/supabase-server.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/whatwg-url","vendor-chunks/cookie","vendor-chunks/webidl-conversions"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fauth%2Fregister%2Froute&page=%2Fapi%2Fauth%2Fregister%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fauth%2Fregister%2Froute.ts&appDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cpeebs%5CDocuments%5Cprojects%5Cp7-comprehensive&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();