"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAuth() {\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Fetch current session and user data\n    const fetchSession = async ()=>{\n        try {\n            console.log(\"AuthContext: Fetching session\");\n            const response = await fetch(\"/api/auth/session\", {\n                method: \"GET\",\n                credentials: \"include\"\n            });\n            if (!response.ok) {\n                console.log(\"AuthContext: No active session\");\n                setUser(null);\n                setSession(null);\n                return;\n            }\n            const data = await response.json();\n            console.log(\"AuthContext: Session fetched successfully\");\n            setUser(data.user);\n            setSession(data.session);\n        } catch (error) {\n            console.error(\"AuthContext: Error fetching session:\", error);\n            setUser(null);\n            setSession(null);\n        }\n    };\n    // Initialize auth state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAuth = async ()=>{\n            console.log(\"AuthContext: Initializing auth\");\n            await fetchSession();\n            setLoading(false);\n        };\n        initializeAuth();\n    }, []);\n    // Sign up function\n    const signUp = async (email, password, userData)=>{\n        try {\n            console.log(\"AuthContext: Attempting signup\");\n            const response = await fetch(\"/api/auth/register\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify({\n                    email,\n                    password,\n                    fullName: userData.full_name,\n                    phone: userData.phone\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                console.error(\"AuthContext: Signup error:\", data.error);\n                return {\n                    data: null,\n                    error: data.error\n                };\n            }\n            console.log(\"AuthContext: Signup successful\");\n            return {\n                data: data.user,\n                error: null\n            };\n        } catch (error) {\n            console.error(\"AuthContext: Signup network error:\", error);\n            return {\n                data: null,\n                error: \"Network error. Please try again.\"\n            };\n        }\n    };\n    // Sign in function\n    const signIn = async (email, password)=>{\n        try {\n            console.log(\"AuthContext: Attempting signin\");\n            const response = await fetch(\"/api/auth/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                console.error(\"AuthContext: Signin error:\", data.error);\n                return {\n                    data: null,\n                    error: data.error\n                };\n            }\n            console.log(\"AuthContext: Signin successful\");\n            // Update local state\n            setUser(data.user);\n            setSession(data.session);\n            return {\n                data: data.user,\n                error: null\n            };\n        } catch (error) {\n            console.error(\"AuthContext: Signin network error:\", error);\n            return {\n                data: null,\n                error: \"Network error. Please try again.\"\n            };\n        }\n    };\n    // Sign out function\n    const signOut = async ()=>{\n        try {\n            console.log(\"AuthContext: Attempting signout\");\n            const response = await fetch(\"/api/auth/logout\", {\n                method: \"POST\",\n                credentials: \"include\"\n            });\n            // Clear local state regardless of API response\n            setUser(null);\n            setSession(null);\n            if (!response.ok) {\n                console.warn(\"AuthContext: Signout API error, but local state cleared\");\n            } else {\n                console.log(\"AuthContext: Signout successful\");\n            }\n            return {\n                error: null\n            };\n        } catch (error) {\n            console.error(\"AuthContext: Signout network error:\", error);\n            // Still clear local state\n            setUser(null);\n            setSession(null);\n            return {\n                error: null\n            }; // Don't fail signout for network errors\n        }\n    };\n    // Reset password function\n    const resetPassword = async (email)=>{\n        try {\n            const { error } = await supabase.auth.resetPasswordForEmail(email, {\n                redirectTo: \"\".concat(window.location.origin, \"/auth/reset-password\")\n            });\n            if (error) {\n                return {\n                    error: error.message\n                };\n            }\n            return {\n                error: null\n            };\n        } catch (error) {\n            return {\n                error: error.message\n            };\n        }\n    };\n    // Update profile function\n    const updateProfile = async (updates)=>{\n        if (!user) {\n            return {\n                data: null,\n                error: \"No user logged in\"\n            };\n        }\n        try {\n            const { data, error } = await supabase.from(\"users\").update({\n                ...updates,\n                updated_at: new Date().toISOString()\n            }).eq(\"id\", user.id).select().single();\n            if (error) {\n                return {\n                    data: null,\n                    error: error.message\n                };\n            }\n            setUser(data);\n            return {\n                data,\n                error: null\n            };\n        } catch (error) {\n            return {\n                data: null,\n                error: error.message\n            };\n        }\n    };\n    // Refresh user data\n    const refreshUser = async ()=>{\n        if (!(session === null || session === void 0 ? void 0 : session.user)) return;\n        const userProfile = await fetchUserProfile(session.user.id);\n        setUser(userProfile);\n    };\n    const value = {\n        user,\n        session,\n        loading,\n        signUp,\n        signIn,\n        signOut,\n        resetPassword,\n        updateProfile,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 240,\n        columnNumber: 5\n    }, this);\n}\n_s1(AuthProvider, \"sIDOCMze9iVqwxkgWIhOu8vskSI=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/AuthContext.tsx\n"));

/***/ })

});