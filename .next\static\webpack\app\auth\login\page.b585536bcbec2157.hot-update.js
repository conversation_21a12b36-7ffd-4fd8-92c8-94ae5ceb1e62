"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/auth/login/page",{

/***/ "(app-pages-browser)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAuth() {\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Fetch current session and user data\n    const fetchSession = async ()=>{\n        try {\n            console.log(\"AuthContext: Fetching session\");\n            const response = await fetch(\"/api/auth/session\", {\n                method: \"GET\",\n                credentials: \"include\"\n            });\n            if (!response.ok) {\n                console.log(\"AuthContext: No active session\");\n                setUser(null);\n                setSession(null);\n                return;\n            }\n            const data = await response.json();\n            console.log(\"AuthContext: Session fetched successfully\");\n            setUser(data.user);\n            setSession(data.session);\n        } catch (error) {\n            console.error(\"AuthContext: Error fetching session:\", error);\n            setUser(null);\n            setSession(null);\n        }\n    };\n    // Initialize auth state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAuth = async ()=>{\n            console.log(\"AuthContext: Initializing auth\");\n            await fetchSession();\n            setLoading(false);\n        };\n        initializeAuth();\n    }, []);\n    // Sign up function\n    const signUp = async (email, password, userData)=>{\n        try {\n            console.log(\"AuthContext: Attempting signup\");\n            const response = await fetch(\"/api/auth/register\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify({\n                    email,\n                    password,\n                    fullName: userData.full_name,\n                    phone: userData.phone\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                console.error(\"AuthContext: Signup error:\", data.error);\n                return {\n                    data: null,\n                    error: data.error\n                };\n            }\n            console.log(\"AuthContext: Signup successful\");\n            return {\n                data: data.user,\n                error: null\n            };\n        } catch (error) {\n            console.error(\"AuthContext: Signup network error:\", error);\n            return {\n                data: null,\n                error: \"Network error. Please try again.\"\n            };\n        }\n    };\n    // Sign in function\n    const signIn = async (email, password)=>{\n        try {\n            console.log(\"AuthContext: Attempting signin\");\n            const response = await fetch(\"/api/auth/login\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                credentials: \"include\",\n                body: JSON.stringify({\n                    email,\n                    password\n                })\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                console.error(\"AuthContext: Signin error:\", data.error);\n                return {\n                    data: null,\n                    error: data.error\n                };\n            }\n            console.log(\"AuthContext: Signin successful\");\n            // Update local state\n            setUser(data.user);\n            setSession(data.session);\n            return {\n                data: data.user,\n                error: null\n            };\n        } catch (error) {\n            console.error(\"AuthContext: Signin network error:\", error);\n            return {\n                data: null,\n                error: \"Network error. Please try again.\"\n            };\n        }\n    };\n    // Google sign in function\n    const signInWithGoogle = async ()=>{\n        try {\n            console.log(\"AuthContext: Attempting Google signin\");\n            const response = await fetch(\"/api/auth/google\", {\n                method: \"POST\",\n                credentials: \"include\"\n            });\n            const data = await response.json();\n            if (!response.ok) {\n                console.error(\"AuthContext: Google signin error:\", data.error);\n                return {\n                    data: null,\n                    error: data.error\n                };\n            }\n            console.log(\"AuthContext: Google OAuth URL generated\");\n            // Redirect to Google OAuth\n            window.location.href = data.url;\n            return {\n                data: data,\n                error: null\n            };\n        } catch (error) {\n            console.error(\"AuthContext: Google signin network error:\", error);\n            return {\n                data: null,\n                error: \"Network error. Please try again.\"\n            };\n        }\n    };\n    // Sign out function\n    const signOut = async ()=>{\n        try {\n            console.log(\"AuthContext: Attempting signout\");\n            const response = await fetch(\"/api/auth/logout\", {\n                method: \"POST\",\n                credentials: \"include\"\n            });\n            // Clear local state regardless of API response\n            setUser(null);\n            setSession(null);\n            if (!response.ok) {\n                console.warn(\"AuthContext: Signout API error, but local state cleared\");\n            } else {\n                console.log(\"AuthContext: Signout successful\");\n            }\n            return {\n                error: null\n            };\n        } catch (error) {\n            console.error(\"AuthContext: Signout network error:\", error);\n            // Still clear local state\n            setUser(null);\n            setSession(null);\n            return {\n                error: null\n            }; // Don't fail signout for network errors\n        }\n    };\n    // Reset password function\n    const resetPassword = async (email)=>{\n        try {\n            console.log(\"AuthContext: Reset password not implemented via API yet\");\n            return {\n                error: \"Password reset functionality will be available soon\"\n            };\n        } catch (error) {\n            return {\n                error: error.message\n            };\n        }\n    };\n    // Update profile function\n    const updateProfile = async (updates)=>{\n        if (!user) {\n            return {\n                data: null,\n                error: \"No user logged in\"\n            };\n        }\n        try {\n            console.log(\"AuthContext: Update profile not implemented via API yet\");\n            return {\n                data: null,\n                error: \"Profile update functionality will be available soon\"\n            };\n        } catch (error) {\n            return {\n                data: null,\n                error: error.message\n            };\n        }\n    };\n    // Refresh user data\n    const refreshUser = async ()=>{\n        console.log(\"AuthContext: Refreshing user data\");\n        await fetchSession();\n    };\n    const value = {\n        user,\n        session,\n        loading,\n        signUp,\n        signIn,\n        signInWithGoogle,\n        signOut,\n        resetPassword,\n        updateProfile,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 247,\n        columnNumber: 5\n    }, this);\n}\n_s1(AuthProvider, \"sIDOCMze9iVqwxkgWIhOu8vskSI=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbnRleHRzL0F1dGhDb250ZXh0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRThFO0FBZ0I5RSxNQUFNSyw0QkFBY0osb0RBQWFBLENBQThCSztBQUV4RCxTQUFTQzs7SUFDZCxNQUFNQyxVQUFVTixpREFBVUEsQ0FBQ0c7SUFDM0IsSUFBSUcsWUFBWUYsV0FBVztRQUN6QixNQUFNLElBQUlHLE1BQU07SUFDbEI7SUFDQSxPQUFPRDtBQUNUO0dBTmdCRDtBQVlULFNBQVNHLGFBQWEsS0FBK0I7UUFBL0IsRUFBRUMsUUFBUSxFQUFxQixHQUEvQjs7SUFDM0IsTUFBTSxDQUFDQyxNQUFNQyxRQUFRLEdBQUdULCtDQUFRQSxDQUFjO0lBQzlDLE1BQU0sQ0FBQ1UsU0FBU0MsV0FBVyxHQUFHWCwrQ0FBUUEsQ0FBYTtJQUNuRCxNQUFNLENBQUNZLFNBQVNDLFdBQVcsR0FBR2IsK0NBQVFBLENBQUM7SUFFdkMsc0NBQXNDO0lBQ3RDLE1BQU1jLGVBQWU7UUFDbkIsSUFBSTtZQUNGQyxRQUFRQyxHQUFHLENBQUM7WUFDWixNQUFNQyxXQUFXLE1BQU1DLE1BQU0scUJBQXFCO2dCQUNoREMsUUFBUTtnQkFDUkMsYUFBYTtZQUNmO1lBRUEsSUFBSSxDQUFDSCxTQUFTSSxFQUFFLEVBQUU7Z0JBQ2hCTixRQUFRQyxHQUFHLENBQUM7Z0JBQ1pQLFFBQVE7Z0JBQ1JFLFdBQVc7Z0JBQ1g7WUFDRjtZQUVBLE1BQU1XLE9BQU8sTUFBTUwsU0FBU00sSUFBSTtZQUNoQ1IsUUFBUUMsR0FBRyxDQUFDO1lBRVpQLFFBQVFhLEtBQUtkLElBQUk7WUFDakJHLFdBQVdXLEtBQUtaLE9BQU87UUFDekIsRUFBRSxPQUFPYyxPQUFPO1lBQ2RULFFBQVFTLEtBQUssQ0FBQyx3Q0FBd0NBO1lBQ3REZixRQUFRO1lBQ1JFLFdBQVc7UUFDYjtJQUNGO0lBRUEsd0JBQXdCO0lBQ3hCWixnREFBU0EsQ0FBQztRQUNSLE1BQU0wQixpQkFBaUI7WUFDckJWLFFBQVFDLEdBQUcsQ0FBQztZQUNaLE1BQU1GO1lBQ05ELFdBQVc7UUFDYjtRQUVBWTtJQUNGLEdBQUcsRUFBRTtJQUVMLG1CQUFtQjtJQUNuQixNQUFNQyxTQUFTLE9BQU9DLE9BQWVDLFVBQWtCQztRQUNyRCxJQUFJO1lBQ0ZkLFFBQVFDLEdBQUcsQ0FBQztZQUNaLE1BQU1DLFdBQVcsTUFBTUMsTUFBTSxzQkFBc0I7Z0JBQ2pEQyxRQUFRO2dCQUNSVyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FWLGFBQWE7Z0JBQ2JXLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFDbkJOO29CQUNBQztvQkFDQU0sVUFBVUwsU0FBU00sU0FBUztvQkFDNUJDLE9BQU9QLFNBQVNPLEtBQUs7Z0JBQ3ZCO1lBQ0Y7WUFFQSxNQUFNZCxPQUFPLE1BQU1MLFNBQVNNLElBQUk7WUFFaEMsSUFBSSxDQUFDTixTQUFTSSxFQUFFLEVBQUU7Z0JBQ2hCTixRQUFRUyxLQUFLLENBQUMsOEJBQThCRixLQUFLRSxLQUFLO2dCQUN0RCxPQUFPO29CQUFFRixNQUFNO29CQUFNRSxPQUFPRixLQUFLRSxLQUFLO2dCQUFDO1lBQ3pDO1lBRUFULFFBQVFDLEdBQUcsQ0FBQztZQUNaLE9BQU87Z0JBQUVNLE1BQU1BLEtBQUtkLElBQUk7Z0JBQUVnQixPQUFPO1lBQUs7UUFDeEMsRUFBRSxPQUFPQSxPQUFZO1lBQ25CVCxRQUFRUyxLQUFLLENBQUMsc0NBQXNDQTtZQUNwRCxPQUFPO2dCQUFFRixNQUFNO2dCQUFNRSxPQUFPO1lBQW1DO1FBQ2pFO0lBQ0Y7SUFFQSxtQkFBbUI7SUFDbkIsTUFBTWEsU0FBUyxPQUFPVixPQUFlQztRQUNuQyxJQUFJO1lBQ0ZiLFFBQVFDLEdBQUcsQ0FBQztZQUNaLE1BQU1DLFdBQVcsTUFBTUMsTUFBTSxtQkFBbUI7Z0JBQzlDQyxRQUFRO2dCQUNSVyxTQUFTO29CQUNQLGdCQUFnQjtnQkFDbEI7Z0JBQ0FWLGFBQWE7Z0JBQ2JXLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztvQkFDbkJOO29CQUNBQztnQkFDRjtZQUNGO1lBRUEsTUFBTU4sT0FBTyxNQUFNTCxTQUFTTSxJQUFJO1lBRWhDLElBQUksQ0FBQ04sU0FBU0ksRUFBRSxFQUFFO2dCQUNoQk4sUUFBUVMsS0FBSyxDQUFDLDhCQUE4QkYsS0FBS0UsS0FBSztnQkFDdEQsT0FBTztvQkFBRUYsTUFBTTtvQkFBTUUsT0FBT0YsS0FBS0UsS0FBSztnQkFBQztZQUN6QztZQUVBVCxRQUFRQyxHQUFHLENBQUM7WUFFWixxQkFBcUI7WUFDckJQLFFBQVFhLEtBQUtkLElBQUk7WUFDakJHLFdBQVdXLEtBQUtaLE9BQU87WUFFdkIsT0FBTztnQkFBRVksTUFBTUEsS0FBS2QsSUFBSTtnQkFBRWdCLE9BQU87WUFBSztRQUN4QyxFQUFFLE9BQU9BLE9BQVk7WUFDbkJULFFBQVFTLEtBQUssQ0FBQyxzQ0FBc0NBO1lBQ3BELE9BQU87Z0JBQUVGLE1BQU07Z0JBQU1FLE9BQU87WUFBbUM7UUFDakU7SUFDRjtJQUVBLDBCQUEwQjtJQUMxQixNQUFNYyxtQkFBbUI7UUFDdkIsSUFBSTtZQUNGdkIsUUFBUUMsR0FBRyxDQUFDO1lBQ1osTUFBTUMsV0FBVyxNQUFNQyxNQUFNLG9CQUFvQjtnQkFDL0NDLFFBQVE7Z0JBQ1JDLGFBQWE7WUFDZjtZQUVBLE1BQU1FLE9BQU8sTUFBTUwsU0FBU00sSUFBSTtZQUVoQyxJQUFJLENBQUNOLFNBQVNJLEVBQUUsRUFBRTtnQkFDaEJOLFFBQVFTLEtBQUssQ0FBQyxxQ0FBcUNGLEtBQUtFLEtBQUs7Z0JBQzdELE9BQU87b0JBQUVGLE1BQU07b0JBQU1FLE9BQU9GLEtBQUtFLEtBQUs7Z0JBQUM7WUFDekM7WUFFQVQsUUFBUUMsR0FBRyxDQUFDO1lBRVosMkJBQTJCO1lBQzNCdUIsT0FBT0MsUUFBUSxDQUFDQyxJQUFJLEdBQUduQixLQUFLb0IsR0FBRztZQUUvQixPQUFPO2dCQUFFcEIsTUFBTUE7Z0JBQU1FLE9BQU87WUFBSztRQUNuQyxFQUFFLE9BQU9BLE9BQVk7WUFDbkJULFFBQVFTLEtBQUssQ0FBQyw2Q0FBNkNBO1lBQzNELE9BQU87Z0JBQUVGLE1BQU07Z0JBQU1FLE9BQU87WUFBbUM7UUFDakU7SUFDRjtJQUVBLG9CQUFvQjtJQUNwQixNQUFNbUIsVUFBVTtRQUNkLElBQUk7WUFDRjVCLFFBQVFDLEdBQUcsQ0FBQztZQUNaLE1BQU1DLFdBQVcsTUFBTUMsTUFBTSxvQkFBb0I7Z0JBQy9DQyxRQUFRO2dCQUNSQyxhQUFhO1lBQ2Y7WUFFQSwrQ0FBK0M7WUFDL0NYLFFBQVE7WUFDUkUsV0FBVztZQUVYLElBQUksQ0FBQ00sU0FBU0ksRUFBRSxFQUFFO2dCQUNoQk4sUUFBUTZCLElBQUksQ0FBQztZQUNmLE9BQU87Z0JBQ0w3QixRQUFRQyxHQUFHLENBQUM7WUFDZDtZQUVBLE9BQU87Z0JBQUVRLE9BQU87WUFBSztRQUN2QixFQUFFLE9BQU9BLE9BQVk7WUFDbkJULFFBQVFTLEtBQUssQ0FBQyx1Q0FBdUNBO1lBQ3JELDBCQUEwQjtZQUMxQmYsUUFBUTtZQUNSRSxXQUFXO1lBQ1gsT0FBTztnQkFBRWEsT0FBTztZQUFLLEdBQUcsd0NBQXdDO1FBQ2xFO0lBQ0Y7SUFFQSwwQkFBMEI7SUFDMUIsTUFBTXFCLGdCQUFnQixPQUFPbEI7UUFDM0IsSUFBSTtZQUNGWixRQUFRQyxHQUFHLENBQUM7WUFDWixPQUFPO2dCQUFFUSxPQUFPO1lBQXNEO1FBQ3hFLEVBQUUsT0FBT0EsT0FBWTtZQUNuQixPQUFPO2dCQUFFQSxPQUFPQSxNQUFNc0IsT0FBTztZQUFDO1FBQ2hDO0lBQ0Y7SUFFQSwwQkFBMEI7SUFDMUIsTUFBTUMsZ0JBQWdCLE9BQU9DO1FBQzNCLElBQUksQ0FBQ3hDLE1BQU07WUFDVCxPQUFPO2dCQUFFYyxNQUFNO2dCQUFNRSxPQUFPO1lBQW9CO1FBQ2xEO1FBRUEsSUFBSTtZQUNGVCxRQUFRQyxHQUFHLENBQUM7WUFDWixPQUFPO2dCQUFFTSxNQUFNO2dCQUFNRSxPQUFPO1lBQXNEO1FBQ3BGLEVBQUUsT0FBT0EsT0FBWTtZQUNuQixPQUFPO2dCQUFFRixNQUFNO2dCQUFNRSxPQUFPQSxNQUFNc0IsT0FBTztZQUFDO1FBQzVDO0lBQ0Y7SUFFQSxvQkFBb0I7SUFDcEIsTUFBTUcsY0FBYztRQUNsQmxDLFFBQVFDLEdBQUcsQ0FBQztRQUNaLE1BQU1GO0lBQ1I7SUFFQSxNQUFNb0MsUUFBeUI7UUFDN0IxQztRQUNBRTtRQUNBRTtRQUNBYztRQUNBVztRQUNBQztRQUNBSztRQUNBRTtRQUNBRTtRQUNBRTtJQUNGO0lBRUEscUJBQ0UsOERBQUNoRCxZQUFZa0QsUUFBUTtRQUFDRCxPQUFPQTtrQkFDMUIzQzs7Ozs7O0FBR1A7SUExTmdCRDtLQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9jb250ZXh0cy9BdXRoQ29udGV4dC50c3g/NmQ4MSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCBSZWFjdCwgeyBjcmVhdGVDb250ZXh0LCB1c2VDb250ZXh0LCB1c2VFZmZlY3QsIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuaW1wb3J0IHR5cGUgeyBVc2VyIH0gZnJvbSAnQC90eXBlcy9kYXRhYmFzZSc7XG5cbmludGVyZmFjZSBBdXRoQ29udGV4dFR5cGUge1xuICB1c2VyOiBVc2VyIHwgbnVsbDtcbiAgc2Vzc2lvbjogYW55IHwgbnVsbDtcbiAgbG9hZGluZzogYm9vbGVhbjtcbiAgc2lnblVwOiAoZW1haWw6IHN0cmluZywgcGFzc3dvcmQ6IHN0cmluZywgdXNlckRhdGE6IFBhcnRpYWw8VXNlcj4pID0+IFByb21pc2U8eyBkYXRhOiBhbnk7IGVycm9yOiBzdHJpbmcgfCBudWxsIH0+O1xuICBzaWduSW46IChlbWFpbDogc3RyaW5nLCBwYXNzd29yZDogc3RyaW5nKSA9PiBQcm9taXNlPHsgZGF0YTogYW55OyBlcnJvcjogc3RyaW5nIHwgbnVsbCB9PjtcbiAgc2lnbkluV2l0aEdvb2dsZTogKCkgPT4gUHJvbWlzZTx7IGRhdGE6IGFueTsgZXJyb3I6IHN0cmluZyB8IG51bGwgfT47XG4gIHNpZ25PdXQ6ICgpID0+IFByb21pc2U8eyBlcnJvcjogc3RyaW5nIHwgbnVsbCB9PjtcbiAgcmVzZXRQYXNzd29yZDogKGVtYWlsOiBzdHJpbmcpID0+IFByb21pc2U8eyBlcnJvcjogc3RyaW5nIHwgbnVsbCB9PjtcbiAgdXBkYXRlUHJvZmlsZTogKHVwZGF0ZXM6IFBhcnRpYWw8VXNlcj4pID0+IFByb21pc2U8eyBkYXRhOiBVc2VyIHwgbnVsbDsgZXJyb3I6IHN0cmluZyB8IG51bGwgfT47XG4gIHJlZnJlc2hVc2VyOiAoKSA9PiBQcm9taXNlPHZvaWQ+O1xufVxuXG5jb25zdCBBdXRoQ29udGV4dCA9IGNyZWF0ZUNvbnRleHQ8QXV0aENvbnRleHRUeXBlIHwgdW5kZWZpbmVkPih1bmRlZmluZWQpO1xuXG5leHBvcnQgZnVuY3Rpb24gdXNlQXV0aCgpIHtcbiAgY29uc3QgY29udGV4dCA9IHVzZUNvbnRleHQoQXV0aENvbnRleHQpO1xuICBpZiAoY29udGV4dCA9PT0gdW5kZWZpbmVkKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKCd1c2VBdXRoIG11c3QgYmUgdXNlZCB3aXRoaW4gYW4gQXV0aFByb3ZpZGVyJyk7XG4gIH1cbiAgcmV0dXJuIGNvbnRleHQ7XG59XG5cbmludGVyZmFjZSBBdXRoUHJvdmlkZXJQcm9wcyB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59XG5cbmV4cG9ydCBmdW5jdGlvbiBBdXRoUHJvdmlkZXIoeyBjaGlsZHJlbiB9OiBBdXRoUHJvdmlkZXJQcm9wcykge1xuICBjb25zdCBbdXNlciwgc2V0VXNlcl0gPSB1c2VTdGF0ZTxVc2VyIHwgbnVsbD4obnVsbCk7XG4gIGNvbnN0IFtzZXNzaW9uLCBzZXRTZXNzaW9uXSA9IHVzZVN0YXRlPGFueSB8IG51bGw+KG51bGwpO1xuICBjb25zdCBbbG9hZGluZywgc2V0TG9hZGluZ10gPSB1c2VTdGF0ZSh0cnVlKTtcblxuICAvLyBGZXRjaCBjdXJyZW50IHNlc3Npb24gYW5kIHVzZXIgZGF0YVxuICBjb25zdCBmZXRjaFNlc3Npb24gPSBhc3luYyAoKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgIGNvbnNvbGUubG9nKCdBdXRoQ29udGV4dDogRmV0Y2hpbmcgc2Vzc2lvbicpO1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCgnL2FwaS9hdXRoL3Nlc3Npb24nLCB7XG4gICAgICAgIG1ldGhvZDogJ0dFVCcsXG4gICAgICAgIGNyZWRlbnRpYWxzOiAnaW5jbHVkZScsXG4gICAgICB9KTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zb2xlLmxvZygnQXV0aENvbnRleHQ6IE5vIGFjdGl2ZSBzZXNzaW9uJyk7XG4gICAgICAgIHNldFVzZXIobnVsbCk7XG4gICAgICAgIHNldFNlc3Npb24obnVsbCk7XG4gICAgICAgIHJldHVybjtcbiAgICAgIH1cblxuICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgIGNvbnNvbGUubG9nKCdBdXRoQ29udGV4dDogU2Vzc2lvbiBmZXRjaGVkIHN1Y2Nlc3NmdWxseScpO1xuXG4gICAgICBzZXRVc2VyKGRhdGEudXNlcik7XG4gICAgICBzZXRTZXNzaW9uKGRhdGEuc2Vzc2lvbik7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0F1dGhDb250ZXh0OiBFcnJvciBmZXRjaGluZyBzZXNzaW9uOicsIGVycm9yKTtcbiAgICAgIHNldFVzZXIobnVsbCk7XG4gICAgICBzZXRTZXNzaW9uKG51bGwpO1xuICAgIH1cbiAgfTtcblxuICAvLyBJbml0aWFsaXplIGF1dGggc3RhdGVcbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBjb25zdCBpbml0aWFsaXplQXV0aCA9IGFzeW5jICgpID0+IHtcbiAgICAgIGNvbnNvbGUubG9nKCdBdXRoQ29udGV4dDogSW5pdGlhbGl6aW5nIGF1dGgnKTtcbiAgICAgIGF3YWl0IGZldGNoU2Vzc2lvbigpO1xuICAgICAgc2V0TG9hZGluZyhmYWxzZSk7XG4gICAgfTtcblxuICAgIGluaXRpYWxpemVBdXRoKCk7XG4gIH0sIFtdKTtcblxuICAvLyBTaWduIHVwIGZ1bmN0aW9uXG4gIGNvbnN0IHNpZ25VcCA9IGFzeW5jIChlbWFpbDogc3RyaW5nLCBwYXNzd29yZDogc3RyaW5nLCB1c2VyRGF0YTogUGFydGlhbDxVc2VyPikgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZygnQXV0aENvbnRleHQ6IEF0dGVtcHRpbmcgc2lnbnVwJyk7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2F1dGgvcmVnaXN0ZXInLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfSxcbiAgICAgICAgY3JlZGVudGlhbHM6ICdpbmNsdWRlJyxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgIGVtYWlsLFxuICAgICAgICAgIHBhc3N3b3JkLFxuICAgICAgICAgIGZ1bGxOYW1lOiB1c2VyRGF0YS5mdWxsX25hbWUsXG4gICAgICAgICAgcGhvbmU6IHVzZXJEYXRhLnBob25lLFxuICAgICAgICB9KSxcbiAgICAgIH0pO1xuXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0F1dGhDb250ZXh0OiBTaWdudXAgZXJyb3I6JywgZGF0YS5lcnJvcik7XG4gICAgICAgIHJldHVybiB7IGRhdGE6IG51bGwsIGVycm9yOiBkYXRhLmVycm9yIH07XG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKCdBdXRoQ29udGV4dDogU2lnbnVwIHN1Y2Nlc3NmdWwnKTtcbiAgICAgIHJldHVybiB7IGRhdGE6IGRhdGEudXNlciwgZXJyb3I6IG51bGwgfTtcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdBdXRoQ29udGV4dDogU2lnbnVwIG5ldHdvcmsgZXJyb3I6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIHsgZGF0YTogbnVsbCwgZXJyb3I6ICdOZXR3b3JrIGVycm9yLiBQbGVhc2UgdHJ5IGFnYWluLicgfTtcbiAgICB9XG4gIH07XG5cbiAgLy8gU2lnbiBpbiBmdW5jdGlvblxuICBjb25zdCBzaWduSW4gPSBhc3luYyAoZW1haWw6IHN0cmluZywgcGFzc3dvcmQ6IHN0cmluZykgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZygnQXV0aENvbnRleHQ6IEF0dGVtcHRpbmcgc2lnbmluJyk7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKCcvYXBpL2F1dGgvbG9naW4nLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7XG4gICAgICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgICAgfSxcbiAgICAgICAgY3JlZGVudGlhbHM6ICdpbmNsdWRlJyxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoe1xuICAgICAgICAgIGVtYWlsLFxuICAgICAgICAgIHBhc3N3b3JkLFxuICAgICAgICB9KSxcbiAgICAgIH0pO1xuXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ0F1dGhDb250ZXh0OiBTaWduaW4gZXJyb3I6JywgZGF0YS5lcnJvcik7XG4gICAgICAgIHJldHVybiB7IGRhdGE6IG51bGwsIGVycm9yOiBkYXRhLmVycm9yIH07XG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKCdBdXRoQ29udGV4dDogU2lnbmluIHN1Y2Nlc3NmdWwnKTtcblxuICAgICAgLy8gVXBkYXRlIGxvY2FsIHN0YXRlXG4gICAgICBzZXRVc2VyKGRhdGEudXNlcik7XG4gICAgICBzZXRTZXNzaW9uKGRhdGEuc2Vzc2lvbik7XG5cbiAgICAgIHJldHVybiB7IGRhdGE6IGRhdGEudXNlciwgZXJyb3I6IG51bGwgfTtcbiAgICB9IGNhdGNoIChlcnJvcjogYW55KSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdBdXRoQ29udGV4dDogU2lnbmluIG5ldHdvcmsgZXJyb3I6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIHsgZGF0YTogbnVsbCwgZXJyb3I6ICdOZXR3b3JrIGVycm9yLiBQbGVhc2UgdHJ5IGFnYWluLicgfTtcbiAgICB9XG4gIH07XG5cbiAgLy8gR29vZ2xlIHNpZ24gaW4gZnVuY3Rpb25cbiAgY29uc3Qgc2lnbkluV2l0aEdvb2dsZSA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ0F1dGhDb250ZXh0OiBBdHRlbXB0aW5nIEdvb2dsZSBzaWduaW4nKTtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvYXV0aC9nb29nbGUnLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBjcmVkZW50aWFsczogJ2luY2x1ZGUnLFxuICAgICAgfSk7XG5cbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG5cbiAgICAgIGlmICghcmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc29sZS5lcnJvcignQXV0aENvbnRleHQ6IEdvb2dsZSBzaWduaW4gZXJyb3I6JywgZGF0YS5lcnJvcik7XG4gICAgICAgIHJldHVybiB7IGRhdGE6IG51bGwsIGVycm9yOiBkYXRhLmVycm9yIH07XG4gICAgICB9XG5cbiAgICAgIGNvbnNvbGUubG9nKCdBdXRoQ29udGV4dDogR29vZ2xlIE9BdXRoIFVSTCBnZW5lcmF0ZWQnKTtcblxuICAgICAgLy8gUmVkaXJlY3QgdG8gR29vZ2xlIE9BdXRoXG4gICAgICB3aW5kb3cubG9jYXRpb24uaHJlZiA9IGRhdGEudXJsO1xuXG4gICAgICByZXR1cm4geyBkYXRhOiBkYXRhLCBlcnJvcjogbnVsbCB9O1xuICAgIH0gY2F0Y2ggKGVycm9yOiBhbnkpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0F1dGhDb250ZXh0OiBHb29nbGUgc2lnbmluIG5ldHdvcmsgZXJyb3I6JywgZXJyb3IpO1xuICAgICAgcmV0dXJuIHsgZGF0YTogbnVsbCwgZXJyb3I6ICdOZXR3b3JrIGVycm9yLiBQbGVhc2UgdHJ5IGFnYWluLicgfTtcbiAgICB9XG4gIH07XG5cbiAgLy8gU2lnbiBvdXQgZnVuY3Rpb25cbiAgY29uc3Qgc2lnbk91dCA9IGFzeW5jICgpID0+IHtcbiAgICB0cnkge1xuICAgICAgY29uc29sZS5sb2coJ0F1dGhDb250ZXh0OiBBdHRlbXB0aW5nIHNpZ25vdXQnKTtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goJy9hcGkvYXV0aC9sb2dvdXQnLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBjcmVkZW50aWFsczogJ2luY2x1ZGUnLFxuICAgICAgfSk7XG5cbiAgICAgIC8vIENsZWFyIGxvY2FsIHN0YXRlIHJlZ2FyZGxlc3Mgb2YgQVBJIHJlc3BvbnNlXG4gICAgICBzZXRVc2VyKG51bGwpO1xuICAgICAgc2V0U2Vzc2lvbihudWxsKTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zb2xlLndhcm4oJ0F1dGhDb250ZXh0OiBTaWdub3V0IEFQSSBlcnJvciwgYnV0IGxvY2FsIHN0YXRlIGNsZWFyZWQnKTtcbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGNvbnNvbGUubG9nKCdBdXRoQ29udGV4dDogU2lnbm91dCBzdWNjZXNzZnVsJyk7XG4gICAgICB9XG5cbiAgICAgIHJldHVybiB7IGVycm9yOiBudWxsIH07XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgY29uc29sZS5lcnJvcignQXV0aENvbnRleHQ6IFNpZ25vdXQgbmV0d29yayBlcnJvcjonLCBlcnJvcik7XG4gICAgICAvLyBTdGlsbCBjbGVhciBsb2NhbCBzdGF0ZVxuICAgICAgc2V0VXNlcihudWxsKTtcbiAgICAgIHNldFNlc3Npb24obnVsbCk7XG4gICAgICByZXR1cm4geyBlcnJvcjogbnVsbCB9OyAvLyBEb24ndCBmYWlsIHNpZ25vdXQgZm9yIG5ldHdvcmsgZXJyb3JzXG4gICAgfVxuICB9O1xuXG4gIC8vIFJlc2V0IHBhc3N3b3JkIGZ1bmN0aW9uXG4gIGNvbnN0IHJlc2V0UGFzc3dvcmQgPSBhc3luYyAoZW1haWw6IHN0cmluZykgPT4ge1xuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZygnQXV0aENvbnRleHQ6IFJlc2V0IHBhc3N3b3JkIG5vdCBpbXBsZW1lbnRlZCB2aWEgQVBJIHlldCcpO1xuICAgICAgcmV0dXJuIHsgZXJyb3I6ICdQYXNzd29yZCByZXNldCBmdW5jdGlvbmFsaXR5IHdpbGwgYmUgYXZhaWxhYmxlIHNvb24nIH07XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgcmV0dXJuIHsgZXJyb3I6IGVycm9yLm1lc3NhZ2UgfTtcbiAgICB9XG4gIH07XG5cbiAgLy8gVXBkYXRlIHByb2ZpbGUgZnVuY3Rpb25cbiAgY29uc3QgdXBkYXRlUHJvZmlsZSA9IGFzeW5jICh1cGRhdGVzOiBQYXJ0aWFsPFVzZXI+KSA9PiB7XG4gICAgaWYgKCF1c2VyKSB7XG4gICAgICByZXR1cm4geyBkYXRhOiBudWxsLCBlcnJvcjogJ05vIHVzZXIgbG9nZ2VkIGluJyB9O1xuICAgIH1cblxuICAgIHRyeSB7XG4gICAgICBjb25zb2xlLmxvZygnQXV0aENvbnRleHQ6IFVwZGF0ZSBwcm9maWxlIG5vdCBpbXBsZW1lbnRlZCB2aWEgQVBJIHlldCcpO1xuICAgICAgcmV0dXJuIHsgZGF0YTogbnVsbCwgZXJyb3I6ICdQcm9maWxlIHVwZGF0ZSBmdW5jdGlvbmFsaXR5IHdpbGwgYmUgYXZhaWxhYmxlIHNvb24nIH07XG4gICAgfSBjYXRjaCAoZXJyb3I6IGFueSkge1xuICAgICAgcmV0dXJuIHsgZGF0YTogbnVsbCwgZXJyb3I6IGVycm9yLm1lc3NhZ2UgfTtcbiAgICB9XG4gIH07XG5cbiAgLy8gUmVmcmVzaCB1c2VyIGRhdGFcbiAgY29uc3QgcmVmcmVzaFVzZXIgPSBhc3luYyAoKSA9PiB7XG4gICAgY29uc29sZS5sb2coJ0F1dGhDb250ZXh0OiBSZWZyZXNoaW5nIHVzZXIgZGF0YScpO1xuICAgIGF3YWl0IGZldGNoU2Vzc2lvbigpO1xuICB9O1xuXG4gIGNvbnN0IHZhbHVlOiBBdXRoQ29udGV4dFR5cGUgPSB7XG4gICAgdXNlcixcbiAgICBzZXNzaW9uLFxuICAgIGxvYWRpbmcsXG4gICAgc2lnblVwLFxuICAgIHNpZ25JbixcbiAgICBzaWduSW5XaXRoR29vZ2xlLFxuICAgIHNpZ25PdXQsXG4gICAgcmVzZXRQYXNzd29yZCxcbiAgICB1cGRhdGVQcm9maWxlLFxuICAgIHJlZnJlc2hVc2VyLFxuICB9O1xuXG4gIHJldHVybiAoXG4gICAgPEF1dGhDb250ZXh0LlByb3ZpZGVyIHZhbHVlPXt2YWx1ZX0+XG4gICAgICB7Y2hpbGRyZW59XG4gICAgPC9BdXRoQ29udGV4dC5Qcm92aWRlcj5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJSZWFjdCIsImNyZWF0ZUNvbnRleHQiLCJ1c2VDb250ZXh0IiwidXNlRWZmZWN0IiwidXNlU3RhdGUiLCJBdXRoQ29udGV4dCIsInVuZGVmaW5lZCIsInVzZUF1dGgiLCJjb250ZXh0IiwiRXJyb3IiLCJBdXRoUHJvdmlkZXIiLCJjaGlsZHJlbiIsInVzZXIiLCJzZXRVc2VyIiwic2Vzc2lvbiIsInNldFNlc3Npb24iLCJsb2FkaW5nIiwic2V0TG9hZGluZyIsImZldGNoU2Vzc2lvbiIsImNvbnNvbGUiLCJsb2ciLCJyZXNwb25zZSIsImZldGNoIiwibWV0aG9kIiwiY3JlZGVudGlhbHMiLCJvayIsImRhdGEiLCJqc29uIiwiZXJyb3IiLCJpbml0aWFsaXplQXV0aCIsInNpZ25VcCIsImVtYWlsIiwicGFzc3dvcmQiLCJ1c2VyRGF0YSIsImhlYWRlcnMiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsImZ1bGxOYW1lIiwiZnVsbF9uYW1lIiwicGhvbmUiLCJzaWduSW4iLCJzaWduSW5XaXRoR29vZ2xlIiwid2luZG93IiwibG9jYXRpb24iLCJocmVmIiwidXJsIiwic2lnbk91dCIsIndhcm4iLCJyZXNldFBhc3N3b3JkIiwibWVzc2FnZSIsInVwZGF0ZVByb2ZpbGUiLCJ1cGRhdGVzIiwicmVmcmVzaFVzZXIiLCJ2YWx1ZSIsIlByb3ZpZGVyIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/AuthContext.tsx\n"));

/***/ })

});