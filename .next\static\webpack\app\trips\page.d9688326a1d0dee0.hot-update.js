"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/trips/page",{

/***/ "(app-pages-browser)/./contexts/AuthContext.tsx":
/*!**********************************!*\
  !*** ./contexts/AuthContext.tsx ***!
  \**********************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ useAuth,AuthProvider auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction useAuth() {\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s1();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    // Fetch current session and user data\n    const fetchSession = async ()=>{\n        try {\n            console.log(\"AuthContext: Fetching session\");\n            const response = await fetch(\"/api/auth/session\", {\n                method: \"GET\",\n                credentials: \"include\"\n            });\n            if (!response.ok) {\n                console.log(\"AuthContext: No active session\");\n                setUser(null);\n                setSession(null);\n                return;\n            }\n            const data = await response.json();\n            console.log(\"AuthContext: Session fetched successfully\");\n            setUser(data.user);\n            setSession(data.session);\n        } catch (error) {\n            console.error(\"AuthContext: Error fetching session:\", error);\n            setUser(null);\n            setSession(null);\n        }\n    };\n    // Initialize auth state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAuth = async ()=>{\n            console.log(\"AuthContext: Initializing auth\");\n            await fetchSession();\n            setLoading(false);\n        };\n        initializeAuth();\n    }, []);\n    // Sign up function\n    const signUp = async (email, password, userData)=>{\n        try {\n            const { data, error } = await supabase.auth.signUp({\n                email,\n                password,\n                options: {\n                    data: {\n                        full_name: userData.full_name,\n                        phone: userData.phone\n                    }\n                }\n            });\n            if (error) {\n                return {\n                    data: null,\n                    error: error.message\n                };\n            }\n            // Create user profile\n            if (data.user) {\n                const { error: profileError } = await supabase.from(\"users\").insert({\n                    id: data.user.id,\n                    email: data.user.email,\n                    full_name: userData.full_name,\n                    phone: userData.phone,\n                    role: \"customer\"\n                });\n                if (profileError) {\n                    console.error(\"Error creating user profile:\", profileError);\n                    return {\n                        data: null,\n                        error: \"Failed to create user profile\"\n                    };\n                }\n            }\n            return {\n                data,\n                error: null\n            };\n        } catch (error) {\n            return {\n                data: null,\n                error: error.message\n            };\n        }\n    };\n    // Sign in function\n    const signIn = async (email, password)=>{\n        try {\n            const { data, error } = await supabase.auth.signInWithPassword({\n                email,\n                password\n            });\n            if (error) {\n                return {\n                    data: null,\n                    error: error.message\n                };\n            }\n            return {\n                data,\n                error: null\n            };\n        } catch (error) {\n            return {\n                data: null,\n                error: error.message\n            };\n        }\n    };\n    // Sign out function\n    const signOut = async ()=>{\n        try {\n            const { error } = await supabase.auth.signOut();\n            if (error) {\n                return {\n                    error: error.message\n                };\n            }\n            return {\n                error: null\n            };\n        } catch (error) {\n            return {\n                error: error.message\n            };\n        }\n    };\n    // Reset password function\n    const resetPassword = async (email)=>{\n        try {\n            const { error } = await supabase.auth.resetPasswordForEmail(email, {\n                redirectTo: \"\".concat(window.location.origin, \"/auth/reset-password\")\n            });\n            if (error) {\n                return {\n                    error: error.message\n                };\n            }\n            return {\n                error: null\n            };\n        } catch (error) {\n            return {\n                error: error.message\n            };\n        }\n    };\n    // Update profile function\n    const updateProfile = async (updates)=>{\n        if (!user) {\n            return {\n                data: null,\n                error: \"No user logged in\"\n            };\n        }\n        try {\n            const { data, error } = await supabase.from(\"users\").update({\n                ...updates,\n                updated_at: new Date().toISOString()\n            }).eq(\"id\", user.id).select().single();\n            if (error) {\n                return {\n                    data: null,\n                    error: error.message\n                };\n            }\n            setUser(data);\n            return {\n                data,\n                error: null\n            };\n        } catch (error) {\n            return {\n                data: null,\n                error: error.message\n            };\n        }\n    };\n    // Refresh user data\n    const refreshUser = async ()=>{\n        if (!(session === null || session === void 0 ? void 0 : session.user)) return;\n        const userProfile = await fetchUserProfile(session.user.id);\n        setUser(userProfile);\n    };\n    const value = {\n        user,\n        session,\n        loading,\n        signUp,\n        signIn,\n        signOut,\n        resetPassword,\n        updateProfile,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\projects\\\\p7-comprehensive\\\\contexts\\\\AuthContext.tsx\",\n        lineNumber: 217,\n        columnNumber: 5\n    }, this);\n}\n_s1(AuthProvider, \"sIDOCMze9iVqwxkgWIhOu8vskSI=\");\n_c = AuthProvider;\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./contexts/AuthContext.tsx\n"));

/***/ })

});