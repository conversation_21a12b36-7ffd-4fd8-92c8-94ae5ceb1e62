'use client';

import React, { createContext, useContext, useEffect, useState } from 'react';
import type { User } from '@/types/database';

interface AuthContextType {
  user: User | null;
  session: any | null;
  loading: boolean;
  signUp: (email: string, password: string, userData: Partial<User>) => Promise<{ data: any; error: string | null }>;
  signIn: (email: string, password: string) => Promise<{ data: any; error: string | null }>;
  signInWithGoogle: () => Promise<{ data: any; error: string | null }>;
  signOut: () => Promise<{ error: string | null }>;
  resetPassword: (email: string) => Promise<{ error: string | null }>;
  updateProfile: (updates: Partial<User>) => Promise<{ data: User | null; error: string | null }>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: React.ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<any | null>(null);
  const [loading, setLoading] = useState(true);

  // Fetch current session and user data
  const fetchSession = async () => {
    try {
      console.log('AuthContext: Fetching session');
      const response = await fetch('/api/auth/session', {
        method: 'GET',
        credentials: 'include',
      });

      if (!response.ok) {
        console.log('AuthContext: No active session');
        setUser(null);
        setSession(null);
        return;
      }

      const data = await response.json();
      console.log('AuthContext: Session fetched successfully');

      setUser(data.user);
      setSession(data.session);
    } catch (error) {
      console.error('AuthContext: Error fetching session:', error);
      setUser(null);
      setSession(null);
    }
  };

  // Initialize auth state
  useEffect(() => {
    const initializeAuth = async () => {
      console.log('AuthContext: Initializing auth');
      await fetchSession();
      setLoading(false);
    };

    initializeAuth();
  }, []);

  // Sign up function
  const signUp = async (email: string, password: string, userData: Partial<User>) => {
    try {
      console.log('AuthContext: Attempting signup');
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          email,
          password,
          fullName: userData.full_name,
          phone: userData.phone,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        console.error('AuthContext: Signup error:', data.error);
        return { data: null, error: data.error };
      }

      console.log('AuthContext: Signup successful');
      return { data: data.user, error: null };
    } catch (error: any) {
      console.error('AuthContext: Signup network error:', error);
      return { data: null, error: 'Network error. Please try again.' };
    }
  };

  // Sign in function
  const signIn = async (email: string, password: string) => {
    try {
      console.log('AuthContext: Attempting signin');
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          email,
          password,
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        console.error('AuthContext: Signin error:', data.error);
        return { data: null, error: data.error };
      }

      console.log('AuthContext: Signin successful');

      // Update local state
      setUser(data.user);
      setSession(data.session);

      return { data: data.user, error: null };
    } catch (error: any) {
      console.error('AuthContext: Signin network error:', error);
      return { data: null, error: 'Network error. Please try again.' };
    }
  };

  // Google sign in function
  const signInWithGoogle = async () => {
    try {
      console.log('AuthContext: Attempting Google signin');
      const response = await fetch('/api/auth/google', {
        method: 'POST',
        credentials: 'include',
      });

      const data = await response.json();

      if (!response.ok) {
        console.error('AuthContext: Google signin error:', data.error);
        return { data: null, error: data.error };
      }

      console.log('AuthContext: Google OAuth URL generated');

      // Redirect to Google OAuth
      window.location.href = data.url;

      return { data: data, error: null };
    } catch (error: any) {
      console.error('AuthContext: Google signin network error:', error);
      return { data: null, error: 'Network error. Please try again.' };
    }
  };

  // Sign out function
  const signOut = async () => {
    try {
      console.log('AuthContext: Attempting signout');
      const response = await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include',
      });

      // Clear local state regardless of API response
      setUser(null);
      setSession(null);

      if (!response.ok) {
        console.warn('AuthContext: Signout API error, but local state cleared');
      } else {
        console.log('AuthContext: Signout successful');
      }

      return { error: null };
    } catch (error: any) {
      console.error('AuthContext: Signout network error:', error);
      // Still clear local state
      setUser(null);
      setSession(null);
      return { error: null }; // Don't fail signout for network errors
    }
  };

  // Reset password function
  const resetPassword = async (email: string) => {
    try {
      console.log('AuthContext: Reset password not implemented via API yet');
      return { error: 'Password reset functionality will be available soon' };
    } catch (error: any) {
      return { error: error.message };
    }
  };

  // Update profile function
  const updateProfile = async (updates: Partial<User>) => {
    if (!user) {
      return { data: null, error: 'No user logged in' };
    }

    try {
      console.log('AuthContext: Update profile not implemented via API yet');
      return { data: null, error: 'Profile update functionality will be available soon' };
    } catch (error: any) {
      return { data: null, error: error.message };
    }
  };

  // Refresh user data
  const refreshUser = async () => {
    console.log('AuthContext: Refreshing user data');
    await fetchSession();
  };

  const value: AuthContextType = {
    user,
    session,
    loading,
    signUp,
    signIn,
    signInWithGoogle,
    signOut,
    resetPassword,
    updateProfile,
    refreshUser,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}
